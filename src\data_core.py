import numpy as np
import warnings
from dataclasses import dataclass, field
from typing import Any, Dict, Iterator, List, Optional, Set, Union

@dataclass(frozen=True, repr=False)
class DataInstance:
    """
    An immutable dataclass holding data for a single instance.

    Uses @dataclass(frozen=True) for immutability and provides both
    attribute-style (`instance.param_a`) and dictionary-style
    (`instance['param_a']`) read access.
    """
    _data: Dict[str, Any] = field(init=False, repr=False)

    def __init__(self, **kwargs: Any):
        """Initializes the instance with key-value data."""
        object.__setattr__(self, '_data', kwargs)

    def __getattr__(self, name: str) -> Any:
        """Enables attribute-style read access (e.g., `instance.key`)."""
        try:
            return self._data[name]
        except KeyError:
            raise AttributeError(f"'DataInstance' object has no attribute '{name}'")

    def __getitem__(self, key: str) -> Any:
        """Enables dictionary-style read access (e.g., `instance['key']`)."""
        return self._data[key]

    def __repr__(self) -> str:
        """Provides a clean string representation."""
        return f"DataInstance({self._data})"

    def __len__(self) -> int:
        """Returns the number of parameters."""
        return len(self._data)

    def __iter__(self) -> Iterator[str]:
        """Allows iteration over parameter keys."""
        return iter(self._data)

    def keys(self):
        """Returns the parameter keys."""
        return self._data.keys()

    def values(self):
        """Returns the parameter values."""
        return self._data.values()

    def items(self):
        """Returns the parameter key-value pairs."""
        return self._data.items()

class Dataset:
    """A container for a collection of DataInstance objects."""
    instances: List[DataInstance]
    parameters: Set[str]
    _fix_integrity: bool = True

    def __init__(self, instances: Optional[List[DataInstance]] = None):
        """
        Initializes the Dataset.

        Args:
            instances: An optional list of DataInstance objects.
        """
        self.instances = instances if instances is not None else []
        if self.instances:
            self.parameters = set().union(*(d.keys() for d in self.instances))
            self._verify_integrity()
        else:
            self.parameters = set()

    def _verify_integrity(self):
        """
        Ensures all instances have the same set of parameters.

        When _fix_integrity=True: Automatically fixes integrity issues by filling
        missing parameters with numpy.nan values.
        When _fix_integrity=False: Only verifies integrity and raises an exception
        if mismatched parameters are detected.
        """
        # First pass: collect all integrity issues for efficient processing
        integrity_issues = []
        for idx, instance in enumerate(self.instances):
            instance_keys = set(instance.keys())
            if self.parameters != instance_keys:
                missing_keys = self.parameters - instance_keys
                extra_keys = instance_keys - self.parameters
                if missing_keys or extra_keys:
                    integrity_issues.append({
                        'index': idx,
                        'instance': instance,
                        'missing_keys': missing_keys,
                        'extra_keys': extra_keys
                    })

        # If no issues found, return early for efficiency
        if not integrity_issues:
            return

        # Handle based on _fix_integrity setting
        if not self._fix_integrity:
            # Verification-only mode: raise exception with detailed information
            error_details = []
            for issue in integrity_issues:
                idx = issue['index']
                missing = issue['missing_keys']
                extra = issue['extra_keys']
                details = f"Instance {idx}:"
                if missing:
                    details += f" missing parameters {sorted(missing)}"
                if extra:
                    if missing:
                        details += ","
                    details += f" extra parameters {sorted(extra)}"
                error_details.append(details)

            error_msg = (
                f"Parameter integrity verification failed. Fixing is disabled (_fix_integrity=False). "
                f"Found {len(integrity_issues)} instances with parameter mismatches:\n" +
                "\n".join(error_details)
            )
            raise ValueError(error_msg)

        # Fix integrity mode: apply fixes as before
        new_instances = []
        for idx, instance in enumerate(self.instances):
            # Check if this instance has issues
            issue = next((issue for issue in integrity_issues if issue['index'] == idx), None)
            if issue and issue['missing_keys']:
                # Instance has missing keys - fix by adding NaN values
                missing_keys = issue['missing_keys']
                warnings.warn(f"Instance {idx} missing keys: {sorted(missing_keys)}. Filling with NaN.")
                # Create new dictionary with all current data
                new_data = dict(instance.items())
                # Add missing keys with nan
                for key in missing_keys:
                    new_data[key] = np.nan
                # Replace with new, complete instance
                new_instances.append(DataInstance(**new_data))
            else:
                # Instance is complete or only has extra keys (which we keep)
                new_instances.append(instance)

        self.instances = new_instances


    def append(self, instance: DataInstance):
        """Appends a single instance and updates parameters."""
        self.extend([instance])

    def extend(self, instances: List[DataInstance]):
        """Extends with a list of instances and updates parameters efficiently."""
        if not instances:
            return

        # 1. Find all new parameters introduced by the incoming instances
        new_params = set().union(*(d.keys() for d in instances))
        truly_new = new_params - self.parameters
        missing_from_new = self.parameters - new_params

        # 2. If there are new parameters, patch all OLD instances
        if truly_new and self._fix_integrity:
            for key in truly_new:
                warnings.warn(f"New parameter '{key}' discovered. Back-filling existing instances with NaN.")
            
            updated_old_instances = []
            for instance in self.instances:
                current_data = dict(instance.items())
                needs_update = False
                for key in truly_new:
                    if key not in current_data:
                        current_data[key] = np.nan
                        needs_update = True
                
                if needs_update:
                    updated_old_instances.append(DataInstance(**current_data))
                else:
                    updated_old_instances.append(instance)
            self.instances = updated_old_instances

        # 3. Patch all NEW instances with any parameters they might be missing
        updated_new_instances = []
        if missing_from_new and self._fix_integrity:
            for instance in instances:
                current_data = dict(instance.items())
                needs_update = False
                for key in missing_from_new:
                    if key not in current_data:
                        warnings.warn(f"Instance {instance} missing key: '{key}'. Filling with NaN.")
                        current_data[key] = np.nan
                        needs_update = True

                if needs_update:
                    updated_new_instances.append(DataInstance(**current_data))
                else:
                    updated_new_instances.append(instance)
        else:
            updated_new_instances = instances # No patching needed

        self.instances.extend(updated_new_instances)
        self.parameters.update(new_params)
        # The verification-only (_fix_integrity=False) logic would need to be integrated here as well.

    def add(self, data: Union[DataInstance, List[DataInstance]]):
        """Adds a single instance or a list of instances."""
        if isinstance(data, DataInstance):
            self.append(data)
        elif isinstance(data, list):
            self.extend(data)
        else:
            raise TypeError(f"Expected DataInstance or list of DataInstance, got {type(data)}")

    def __repr__(self) -> str:
        """Provides a summary of the dataset."""
        num_instances = len(self.instances)
        num_params = len(self.parameters)
        param_str = f"{num_params} parameters"
        instance_str = f"{num_instances} instances"

        header = f"<Dataset: {instance_str}, {param_str}>"
        if num_instances == 0:
            return header

        # Show first 5 instances as examples
        examples = "\nExamples:\n"
        for i, instance in enumerate(self.instances[:5]):
            examples += f"  [{i}] {instance}\n"
        return header + examples

    def get_param(self, key: str) -> List[Any]:
        """Gets all values for a single parameter, returns a list."""
        if key not in self.parameters:
            raise KeyError(f"Parameter '{key}' not found in dataset.")
        return [instance.get(key, np.nan) for instance in self.instances]

   def get(self, *keys: str) -> np.ndarray:
    """
    Gets data for multiple parameters as a NumPy array.
    """
    # Verify all keys exist first to fail fast
    for key in keys:
        if key not in self.parameters:
            raise KeyError(f"Parameter '{key}' not found in dataset.")

    # Single iteration over the data
    data_rows = [
        [instance.get(key, np.nan) for key in keys]
        for instance in self.instances
    ]
    return np.array(data_rows)

# --- DEMONSTRATION ---
if __name__ == "__main__":
    print("--- 1. Creating individual DataInstances ---")
    d1 = DataInstance(param_a=1, param_b=10.5, category='A')
    d2 = DataInstance(param_a=2, param_b=12.1, category='A')
    # This instance is missing 'param_b' and has an extra 'param_c'
    d3 = DataInstance(param_a=3, category='B', param_c=100)
    print(d1)
    print(d2)
    print(d3)

    print("\n--- 2. Creating a Dataset ---")
    # The integrity check will be triggered here
    dataset = Dataset([d1, d2, d3])
    print(dataset)
    print(f"All parameters in dataset: {sorted(list(dataset.parameters))}")

    print("\n--- 3. Adding new data ---")
    d4 = DataInstance(param_a=4, param_b=15.0, category='B', param_c=101)
    dataset.add(d4)
    print(dataset)

    print("\n--- 4. Retrieving data ---")
    # Get a single parameter as a list
    categories = dataset.get_param('category')
    print(f"Categories: {categories}")

    # Get multiple parameters as a NumPy array
    numerical_data = dataset.get('param_a', 'param_b')
    print("Numerical data (param_a, param_b):\n", numerical_data)

    # Note that d3's 'param_b' is now np.nan
    assert np.isnan(numerical_data[2, 1])
    print("\nAssertion successful: Missing value was correctly filled with NaN.")
